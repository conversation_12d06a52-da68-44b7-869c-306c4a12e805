import 'dart:math';

import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/game/components/menu_button.dart';

/// Center‑aligned, adaptive grid of menu buttons with orientation handling.
class MenuGrid extends PositionComponent with HasGameRef {
  final List<String> buttonAssets;
  final List<VoidCallback> buttonCallbacks;
  final bool isFullGamePurchased;

  late final List<MenuButton> buttons;

  // Grid constants
  static const int columns = 5;
  static const int rows = 3;

  /// Spacing between buttons. Adjusted for the current device.
  double buttonMargin = 15;

  // ADDED: Track last layout to detect changes
  Vector2? _lastGameSize;
  bool _needsRelayout = false;

  MenuGrid({
    required this.buttonAssets,
    required this.buttonCallbacks,
    this.isFullGamePurchased = false,
  });

  // ------------------------------- LIFECYCLE ---------------------------------

  @override
  Future<void> onLoad() async {
    buttons = List.generate(
      buttonAssets.length,
          (index) => MenuButton(
        buttonAssets[index],
        buttonCallbacks[index],
        isLocked: index >= AppConstants.FREE_GAMES_COUNT && !isFullGamePurchased,
      ),
    );

    // Add all buttons to the component tree.
    for (final button in buttons) add(button);

    // Wait one frame so Flame knows the viewport size.
    await Future.delayed(Duration.zero);
    _layoutButtons();
    _animateButtonsIn();

    if (kDebugMode) {
      print('🎯 MenuGrid loaded with ${buttons.length} buttons');
      print('   isFullGamePurchased: $isFullGamePurchased');
      for (int i = 0; i < buttons.length; i++) {
        print('   Button $i: isLocked=${buttons[i].isLocked}');
      }
    }
  }

  @override
  void update(double dt) {
    super.update(dt);

    // ADDED: Check if we need to relayout due to orientation change
    if (_needsRelayout) {
      _needsRelayout = false;
      _layoutButtons();
    }
  }

  // -------------------------------- LAYOUT ----------------------------------

  /// Layout buttons so the entire grid fits inside 90 % of the viewport
  /// while remaining perfectly centered.
  void _layoutButtons() {
    // ENHANCED: Better size validation and orientation handling
    final gameSize = gameRef.size;

    // Skip layout if game size is invalid
    if (gameSize.x <= 0 || gameSize.y <= 0) {
      // Schedule relayout for next frame
      _needsRelayout = true;
      return;
    }

    // Check if size actually changed
    if (_lastGameSize != null &&
        (_lastGameSize!.x - gameSize.x).abs() < 1.0 &&
        (_lastGameSize!.y - gameSize.y).abs() < 1.0) {
      return; // No significant change, skip relayout
    }

    _lastGameSize = gameSize.clone();

    // Logical viewport size (device‑independent px).
    final gameWidth = gameSize.x;
    final gameHeight = gameSize.y;

    // ENHANCED: Better adaptation for different orientations
    _setAdaptiveMargin(gameWidth, gameHeight);

    // Available frame (90 % of each dimension).
    final frameWidth = gameWidth * 0.9;
    final frameHeight = gameHeight * 0.9;

    // ENHANCED: Better size calculation for landscape orientation
    final sizeByWidth = (frameWidth - buttonMargin * (columns - 1)) / columns;
    final sizeByHeight = (frameHeight - buttonMargin * (rows - 1)) / rows;
    final buttonSize = min(sizeByWidth, sizeByHeight);

    // ADDED: Ensure minimum button size for usability
    final clampedButtonSize = buttonSize.clamp(50.0, 150.0);

    // Dimensions of the resulting grid.
    final gridWidth = clampedButtonSize * columns + buttonMargin * (columns - 1);
    final gridHeight = clampedButtonSize * rows + buttonMargin * (rows - 1);

    // Center the grid inside the **full** viewport.
    final startX = (gameWidth - gridWidth) / 2;
    final startY = (gameHeight - gridHeight) / 2;

    // Position each button by its center (MenuButton uses Anchor.center).
    for (var i = 0; i < buttons.length; i++) {
      final row = i ~/ columns;
      final col = i % columns;

      final centerX =
          startX + col * (clampedButtonSize + buttonMargin) + clampedButtonSize / 2;
      final centerY =
          startY + row * (clampedButtonSize + buttonMargin) + clampedButtonSize / 2;

      buttons[i]
        ..anchor = Anchor.center
        ..position = Vector2(centerX, centerY)
        ..size = Vector2.all(clampedButtonSize);
    }

    if (kDebugMode) {
      print('🎯 MenuGrid layout updated:');
      print('   Game size: ${gameWidth}x${gameHeight}');
      print('   Button size: $clampedButtonSize');
      print('   Grid position: ($startX, $startY)');
      print('   Grid size: ${gridWidth}x${gridHeight}');
    }
  }

  /// Choose `buttonMargin` based on the device form‑factor.
  void _setAdaptiveMargin(double width, double height) {
    final aspect = width / height;

    // ENHANCED: Better margin calculation for different screen sizes
    if (aspect > 2.0) {
      // Very wide landscape (like tablets in landscape)
      buttonMargin = 25;
    } else if (aspect > 1.5) {
      // Standard landscape
      buttonMargin = 20;
    } else if (aspect > 1.2) {
      // Narrow landscape or square
      buttonMargin = 15;
    } else {
      // Portrait or nearly square (shouldn't happen in our game)
      buttonMargin = 10;
    }

    // ADDED: Scale margin based on actual screen size
    final minDimension = min(width, height);
    if (minDimension < 600) {
      buttonMargin *= 0.7; // Reduce margin on small screens
    } else if (minDimension > 1200) {
      buttonMargin *= 1.3; // Increase margin on large screens
    }
  }

  // -------------------------------- EFFECTS ---------------------------------

  void _animateButtonsIn() {
    for (var i = 0; i < buttons.length; i++) {
      final button = buttons[i];

      button
        ..scale = Vector2.all(0.1)
        ..opacity = 0;

      button.add(
        SequenceEffect([
          ScaleEffect.to(
            Vector2.all(1.2),
            EffectController(
              duration: 0.4,
              startDelay: 0.08 * i,
              curve: Curves.easeOutBack,
            ),
          ),
          ScaleEffect.to(
            Vector2.all(1.0),
            EffectController(
              duration: 0.2,
              curve: Curves.easeInOut,
            ),
          ),
        ]),
      );

      button.add(
        OpacityEffect.fadeIn(
          EffectController(
            duration: 0.3,
            startDelay: 0.08 * i,
            curve: Curves.easeIn,
          ),
        ),
      );
    }
  }

  // -------------------------------- HELPERS ---------------------------------

  void unlockAllGames() {
    for (final button in buttons) {
      button.unlock();
    }

    if (kDebugMode) {
      print('🔓 Unlocked all games in MenuGrid');
    }
  }

  /// ENHANCED: Handle game resize with debouncing to prevent layout thrashing
  @override
  void onGameResize(Vector2 size) {
    super.onGameResize(size);

    // ADDED: Debounce resize events to prevent excessive relayouts
    Future.delayed(const Duration(milliseconds: 100), () {
      if (isMounted) {
        _needsRelayout = true;
      }
    });
  }

  /// ADDED: Force immediate relayout (useful after orientation changes)
  void forceRelayout() {
    if (isMounted) {
      _layoutButtons();
      if (kDebugMode) {
        print('🔄 Forced MenuGrid relayout');
      }
    }
  }

  /// ADDED: Method to refresh button states after purchase
  void refreshButtonStates(bool isFullGamePurchased) {
    if (kDebugMode) {
      print('🔄 Refreshing button states, isFullGamePurchased: $isFullGamePurchased');
    }

    for (var i = 0; i < buttons.length; i++) {
      final button = buttons[i];
      final shouldBeLocked = i >= AppConstants.FREE_GAMES_COUNT && !isFullGamePurchased;

      if (kDebugMode) {
        print('   Button $i: shouldBeLocked=$shouldBeLocked, currentlyLocked=${button.isLocked}');
      }

      if (button.isLocked && !shouldBeLocked) {
        if (kDebugMode) {
          print('   Unlocking button $i');
        }
        button.unlock();
      } else if (!button.isLocked && shouldBeLocked) {
        // This shouldn't happen, but handle it just in case
        button.isLocked = true;
      }
    }

    // Force a complete re-layout after all buttons are updated
    _layoutButtons();
  }

  /// ADDED: Force complete visual refresh of all buttons
  void forceCompleteRefresh() {
    if (kDebugMode) {
      print('🔄 Force complete refresh of all buttons');
    }

    // Remove all buttons from the component tree
    for (final button in buttons) {
      if (children.contains(button)) {
        remove(button);
      }
    }

    // Re-add all buttons to force complete re-render
    for (final button in buttons) {
      add(button);
    }

    // Force layout update
    _layoutButtons();

    // Re-apply animations
    _animateButtonsIn();
  }
}