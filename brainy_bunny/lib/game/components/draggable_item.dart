import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flame/events.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/game/components/drag_target_item.dart';
import 'package:brainy_bunny/game/games/drag_game.dart';

class DraggableItem extends SpriteComponent with DragCallbacks {
  static const int HIGH_PRIORITY = 100;

  bool isDragging = false;
  final int index;
  final Vector2 initialPosition;
  final DragGame gameRef;
  Vector2? dragStartPosition;
  bool _isMatched = false;
  Vector2? _startPosition; // Position when dragging started

  DraggableItem({
    required this.index,
    required Sprite sprite,
    required this.initialPosition,
    required Vector2 size,
    required this.gameRef,
  }) : super(sprite: sprite, position: initialPosition.clone(), size: size) {
    priority = HIGH_PRIORITY; // Set high priority to draw on top
  }

  @override
  void onMount() {
    super.onMount();
    // Store the actual position after the component is mounted and positioned
    _startPosition = position.clone();

    if (kDebugMode) {
      print('Mounted item $index at position $position with anchor $anchor');
    }
  }

  @override
  void onDragStart(DragStartEvent event) {
    super.onDragStart(event);

    if (_isMatched) return; // Don't allow dragging if already matched

    isDragging = true;

    // Always store the current position when drag starts
    // This is the position we'll return to if not matched
    _startPosition = position.clone();
    dragStartPosition = position.clone();

    if (kDebugMode) {
      print(
          'Started dragging item $index from position $position (anchor: $anchor)');
    }
  }

  @override
  void onDragUpdate(DragUpdateEvent event) {
    if (_isMatched || !isDragging) return;

    // Use the delta (change) from the event to update position
    // This is more reliable across Flame versions
    position.add(event.canvasDelta);
  }

  @override
  void onDragEnd(DragEndEvent event) {
    super.onDragEnd(event);

    if (_isMatched) return;

    isDragging = false;

    if (kDebugMode) {
      print('Ended dragging item $index at position $position');
    }

    // Check if dropped on correct target
    if (!isAccepted()) {
      resetPosition();
    }

    dragStartPosition = null;
  }

  @override
  void onDragCancel(DragCancelEvent event) {
    super.onDragCancel(event);

    if (_isMatched) return;

    isDragging = false;
    dragStartPosition = null;
    resetPosition();

    if (kDebugMode) {
      print('Cancelled dragging item $index');
    }
  }

  /// Check if this item is accepted by any target
  bool isAccepted() {
    if (_isMatched) return true;

    // Try to match with targets
    for (final target in gameRef.children.whereType<DragTargetItem>()) {
      // Skip targets that are already matched
      if (target.isMatched) continue;

      // Check if this item can match with the target
      if (target.matchWith(this)) {
        if (kDebugMode) {
          print('Item $index successfully matched with target ${target.index}');
        }
        return true;
      }
    }

    if (kDebugMode) {
      print('Item $index not accepted by any target');
    }
    return false;
  }

  /// Reset to initial position with animation
  void resetPosition() {
    if (_isMatched) return;

    // Use the stored start position if available, otherwise fall back to calculated center
    final targetPosition = _startPosition ?? position;

    if (kDebugMode) {
      print(
          'Resetting item $index to position $targetPosition (anchor: $anchor)');
    }

    // Add smooth animation back to original position
    add(
      MoveToEffect(
        targetPosition,
        EffectController(
          duration: 0.4,
          curve: Curves.elasticOut,
        ),
      ),
    );

    // Optional: Add a small bounce effect
    add(SequenceEffect([
      ScaleEffect.to(
        Vector2.all(0.9),
        EffectController(duration: 0.2),
      ),
      ScaleEffect.to(
        Vector2.all(1.0),
        EffectController(duration: 0.2),
      ),
    ]));
  }

  /// Mark this item as matched (called by target when match is successful)
  void markAsMatched() {
    _isMatched = true;
    isDragging = false;

    if (kDebugMode) {
      print('Marking item $index as matched');
    }
  }

  /// Hide this item with a fade out animation
  void hide() {
    if (!_isMatched) {
      markAsMatched();
    }

    add(
      OpacityEffect.fadeOut(
        EffectController(duration: 0.5),
      )..onComplete = () {
          if (kDebugMode) {
            print('Hiding item $index');
          }
          removeFromParent();
        },
    );
  }

  /// Check if this item is matched
  bool get isMatched => _isMatched;
}