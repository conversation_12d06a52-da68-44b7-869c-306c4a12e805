import 'package:flame/components.dart';
import 'package:flutter/foundation.dart';
import 'package:brainy_bunny/game/components/draggable_item.dart';
import 'package:brainy_bunny/game/games/drag_game.dart';
import 'package:brainy_bunny/services/audio_service.dart';

class DragTargetItem extends SpriteComponent {
  final Sprite acceptedSprite;
  final DragGame gameRef;
  final int index;

  bool isMatched = false;

  DragTargetItem({
    required this.index,
    required Sprite sprite,
    required this.acceptedSprite,
    required Vector2 position,
    required Vector2 size,
    required this.gameRef,
  }) : super(sprite: sprite, position: position, size: size);

  /// Check if this target matches with the given draggable item
  bool matchWith(DraggableItem draggable) {
    if (isMatched) {
      if (kDebugMode) {
        print('Target $index already matched');
      }
      return false; // Already matched
    }

    if (index != draggable.index) {
      if (kDebugMode) {
        print('Target $index: wrong draggable ${draggable.index}');
      }
      return false; // Not the correct match
    }

    // Check if the center of the draggable item is within the bounds of this target
    final draggableCenter = draggable.position + draggable.size / 2;
    if (!containsPoint(draggableCenter)) {
      if (kDebugMode) {
        print(
            'Target $index: draggable ${draggable.index} center not within bounds');
      }
      return false; // Not over this target
    }

    // Match successful
    if (kDebugMode) {
      print(
          'Target $index: successful match with draggable ${draggable.index}');
    }

    isMatched = true;
    sprite = acceptedSprite;
    AudioService.instance.playSound('match_sound.wav');

    // Mark the draggable as matched and hide it
    draggable.markAsMatched();
    draggable.hide();

    // Notify the game about the match
    gameRef.onItemMatched();

    return true;
  }

  /// Check if a point is within the bounds of this component
  @override
  bool containsPoint(Vector2 point) {
    final result = point.x >= position.x &&
        point.x <= position.x + size.x &&
        point.y >= position.y &&
        point.y <= position.y + size.y;

    if (kDebugMode && !result) {
      print(
          'Target $index: point $point not within bounds ${position.x}-${position.x + size.x}, ${position.y}-${position.y + size.y}');
    }

    return result;
  }
}