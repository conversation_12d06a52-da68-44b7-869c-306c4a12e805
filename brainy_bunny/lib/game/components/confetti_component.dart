import 'dart:math';
import 'dart:ui' as ui;

import 'package:flame/components.dart';
import 'package:flame/particles.dart';
import 'package:flutter/material.dart';

/// A reusable component that creates gentle confetti rain in a Flame game.
class FlameConfetti extends Component {
  /// Maximum number of particles per burst
  final int maxParticles;

  /// Duration range for particles in seconds (min, max)
  final Vector2 lifespanRange;

  /// Gravity variation (base, variation)
  final Vector2 gravityRange;

  /// Size multiplier for particles (larger values = bigger confetti)
  final double sizeMultiplier;

  /// If true, a timer will automatically create bursts
  final bool autoPlay;

  /// Time between auto-bursts in seconds
  final double autoPlayInterval;

  /// Timer for auto play mode
  Timer? _autoPlayTimer;

  /// Timer for infinite rain duration
  Timer? _rainTimer;

  /// Timer for particle spawning during rain
  Timer? _spawnTimer;

  /// Flag to track if rain is active
  bool _isRaining = false;

  /// Create a confetti component with gentle parameters
  FlameConfetti({
    this.maxParticles = 100,
    Vector2? lifespanRange,
    Vector2? gravityRange,
    this.sizeMultiplier = 1.0,
    this.autoPlay = false,
    this.autoPlayInterval = 3,
  })  : lifespanRange = lifespanRange ?? Vector2(8.0, 10.0),
        // Enough to cross screen
        gravityRange = gravityRange ?? Vector2(60, 20); // Moderate gravity

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    if (autoPlay) {
      _autoPlayTimer = Timer(
        autoPlayInterval,
        onTick: () {
          // Find a random position in the game area for the burst
          final gameSize = findGame()?.size ?? Vector2(400, 600);
          final random = Random();
          final randomPosition = Vector2(
            gameSize.x * (0.2 + random.nextDouble() * 0.6),
            gameSize.y * (0.2 + random.nextDouble() * 0.6),
          );

          burst(randomPosition);
        },
        repeat: true,
      );
    }
  }

  @override
  void update(double dt) {
    super.update(dt);
    _autoPlayTimer?.update(dt);
    _rainTimer?.update(dt);
    _spawnTimer?.update(dt);
  }

  /// Creates a gentle confetti rain from the top of the screen for 6 seconds
  void burst(Vector2 position, {int? particleCount}) {
    if (_isRaining) return; // Prevent multiple rain sessions

    final random = Random();
    final gameSize = findGame()?.size ?? Vector2(800, 600);

    _isRaining = true;

    // Calculate how many particles to spawn per interval
    final totalParticles = particleCount ?? maxParticles;
    const rainDuration = 6.0; // 6 seconds of rain
    const spawnInterval = 0.2; // Spawn particles every 50ms
    final particlesPerSpawn =
        (totalParticles * spawnInterval / rainDuration).round();

    // Timer to stop the rain after 6 seconds
    _rainTimer = Timer(
      rainDuration,
      onTick: () {
        _stopRain();
      },
      repeat: false,
    );

    // Timer to continuously spawn particles
    _spawnTimer = Timer(
      spawnInterval,
      onTick: () {
        if (_isRaining && findGame() != null) {
          for (int i = 0; i < particlesPerSpawn; i++) {
            // Calculate random horizontal position across the screen width
            final xPosition = random.nextDouble() * gameSize.x;
            // Start above the screen with some variation
            final yPosition = -20.0 - random.nextDouble() * 50;

            final particleSystem = ParticleSystemComponent(
              position: Vector2(xPosition, yPosition),
              particle: _createSingleConfetti(random, gameSize),
            );
            findGame()?.add(particleSystem);
          }
        }
      },
      repeat: true,
    );
  }

  /// Stops the continuous rain
  void _stopRain() {
    _isRaining = false;
    _spawnTimer?.stop();
    _spawnTimer = null;
    _rainTimer?.stop();
    _rainTimer = null;
  }

  /// Creates a burst of confetti that follows a component
  void burstFollowingComponent(PositionComponent target,
      {Vector2? offset, int? particleCount}) {
    final random = Random();
    final particlesToEmit = particleCount ?? maxParticles;
    final positionOffset = offset ?? Vector2.zero();

    // Create the particle system at the target's position
    final particleSystem = ParticleSystemComponent(
      position: target.position + positionOffset,
      particle:
          _createSingleConfetti(random, findGame()?.size ?? Vector2(800, 600)),
    );

    // Add the particle system to the game
    findGame()?.add(particleSystem);
  }

  /// Creates a single confetti particle
  Particle _createSingleConfetti(Random random, Vector2 gameSize) {
    return AcceleratedParticle(
      acceleration: Vector2(
          0,
          gravityRange.x +
              random.nextDouble() * gravityRange.y // Gentle downward gravity
          ),
      speed: Vector2(
        // Increased horizontal drift for more natural effect
        (random.nextDouble() - 0.5) * 40,
        // More horizontal movement (-20 to +20)
        // Initial downward speed
        random.nextDouble() * 40 + 20, // 20-60 pixels/second downward
      ),
      child: ComposedParticle(
        children: [
          // Add stronger sideways drift for better spread
          AcceleratedParticle(
            acceleration: Vector2(random.nextDouble() * 8 - 4, 0),
            // Increased sideways drift
            child: MovingParticle(
              from: Vector2(-4, 0),
              to: Vector2(4, 0),
              curve: Curves.easeInOut,
              child: RotatingParticle(
                to: random.nextDouble() * pi * 8 + pi * 2,
                // More rotation (2-10 full turns)
                child: _ConfettiRenderParticle(
                  size: Vector2.all(12 + random.nextDouble() * 16),
                  // Much bigger size (12-28, was 6-16)
                  paint: Paint()
                    ..color = Color.fromRGBO(
                      100 + random.nextInt(155),
                      100 + random.nextInt(155),
                      100 + random.nextInt(155),
                      ((180 + random.nextInt(75)) / 255)
                          .toDouble(), // Brighter transparency
                    ),
                  random: random,
                  gameSize: gameSize,
                ),
              ),
            ),
          ),
        ],
      ),
      // Enough lifespan to cross the screen
      lifespan: lifespanRange.x +
          random.nextDouble() * (lifespanRange.y - lifespanRange.x),
    );
  }

  @override
  void onRemove() {
    _autoPlayTimer?.stop();
    _stopRain();
    super.onRemove();
  }
}

/// The individual confetti particle implementation
class _ConfettiRenderParticle extends Particle {
  final Vector2 size;
  final Random random;
  final Paint paint;
  final Vector2 gameSize;

  late final double _zAxisRotationSpeed;
  late final double _initialZAxisRotation;
  double _currentZAxisRotation = 0;
  late final int _shapeType;
  late final bool _useGradient;
  late final double _maxTwist;
  late final double _twistSpeed;
  double _currentTwist = 0;

  _ConfettiRenderParticle({
    required this.size,
    required this.paint,
    required this.random,
    required this.gameSize,
  }) {
    _zAxisRotationSpeed =
        random.nextDouble() * 8 - 4; // Higher rotation speed (was 4-2)
    _initialZAxisRotation = random.nextDouble() * pi * 2;
    _currentZAxisRotation = _initialZAxisRotation;
    _shapeType = random.nextInt(5); // Now 5 shape types (including hearts)
    _useGradient = random.nextDouble() > 0.3;
    _maxTwist = random.nextDouble() * 0.4; // Increased twist (was 0.3)
    _twistSpeed =
        random.nextDouble() * 6 - 3; // Increased twist speed (was 4-2)
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Update the z-axis rotation
    _currentZAxisRotation += _zAxisRotationSpeed * dt;
    // Update twist effect
    _currentTwist = sin(_twistSpeed * _currentZAxisRotation) * _maxTwist;
  }

  @override
  void render(Canvas canvas) {
    canvas.save();

    // Apply z-axis rotation by scaling the x-axis
    final zEffect = cos(_currentZAxisRotation).abs() * 0.7 + 0.3;
    canvas.scale(zEffect, 1.0);

    // Apply paint based on shape type
    Paint shapePaint = paint;

    // Create gradient paint for some particles
    if (_useGradient) {
      final baseColor = paint.color;
      final lightColor = Color.lerp(baseColor, Colors.white, 0.5)!;
      final gradientPaint = Paint()
        ..shader = ui.Gradient.linear(
          Offset(-size.x / 2, -size.y / 2),
          Offset(size.x / 2, size.y / 2),
          [lightColor, baseColor, lightColor], // Softer gradient
          [0.0, 0.5, 1.0],
        );
      shapePaint = gradientPaint;
    }

    // Apply twist/bend effect to make confetti curve slightly
    if (_shapeType == 0 || _shapeType == 3 || _shapeType == 4) {
      canvas.skew(0, _currentTwist);
    }

    // Draw the shape based on shape type
    if (_shapeType == 0) {
      // Rectangular confetti
      canvas.drawRect(
        Rect.fromLTWH(-size.x / 2, -size.y / 2, size.x, size.y),
        shapePaint,
      );
    } else if (_shapeType == 1) {
      // Circular confetti
      canvas.drawCircle(
        Offset.zero,
        size.x / 2,
        shapePaint,
      );
    } else if (_shapeType == 2) {
      // Diamond/rhombus confetti
      final path = Path();
      path.moveTo(0, -size.y / 2);
      path.lineTo(size.x / 2, 0);
      path.lineTo(0, size.y / 2);
      path.lineTo(-size.x / 2, 0);
      path.close();
      canvas.drawPath(path, shapePaint);
    } else if (_shapeType == 3) {
      // Heart shape - friendly shape for children
      final path = Path();
      final heartSize = size.x / 2;

      // Draw a proper heart shape
      path.moveTo(0, heartSize * 0.3);
      path.cubicTo(-heartSize * 0.5, -heartSize * 0.1, -heartSize,
          heartSize * 0.1, -heartSize * 0.5, heartSize * 0.5);
      path.cubicTo(-heartSize * 0.5, heartSize * 0.8, -heartSize * 0.2,
          heartSize, 0, heartSize * 1.2);
      path.cubicTo(heartSize * 0.2, heartSize, heartSize * 0.5, heartSize * 0.8,
          heartSize * 0.5, heartSize * 0.5);
      path.cubicTo(heartSize, heartSize * 0.1, heartSize * 0.5,
          -heartSize * 0.1, 0, heartSize * 0.3);
      path.close();

      canvas.drawPath(path, shapePaint);
    } else {
      // Star shape
      final path = Path();
      final starSize = size.x / 2;
      const numPoints = 5;

      for (int i = 0; i < numPoints * 2; i++) {
        final angle = i * pi / numPoints;
        final radius = (i % 2 == 0) ? starSize : starSize * 0.5;
        final x = radius * cos(angle - pi / 2);
        final y = radius * sin(angle - pi / 2);

        if (i == 0) {
          path.moveTo(x, y);
        } else {
          path.lineTo(x, y);
        }
      }
      path.close();

      canvas.drawPath(path, shapePaint);
    }

    // Add a gentle glow effect instead of harsh edges
    final glowPaint = Paint()
      ..color = paint.color.withValues(
        alpha: (paint.color.a * 0.3).toDouble(),
        red: paint.color.r.toDouble(),
        green: paint.color.g.toDouble(),
        blue: paint.color.b.toDouble(),
      )
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 2.0);

    if (_shapeType == 0) {
      canvas.drawRect(
        Rect.fromLTWH(-size.x / 2, -size.y / 2, size.x, size.y),
        glowPaint,
      );
    } else if (_shapeType == 1) {
      canvas.drawCircle(
        Offset.zero,
        size.x / 2,
        glowPaint,
      );
    } else if (_shapeType == 2) {
      final path = Path();
      path.moveTo(0, -size.y / 2);
      path.lineTo(size.x / 2, 0);
      path.lineTo(0, size.y / 2);
      path.lineTo(-size.x / 2, 0);
      path.close();
      canvas.drawPath(path, glowPaint);
    } else if (_shapeType == 3) {
      // Heart glow
      final path = Path();
      final heartSize = size.x / 2;

      path.moveTo(0, heartSize * 0.3);
      path.cubicTo(-heartSize * 0.5, -heartSize * 0.1, -heartSize,
          heartSize * 0.1, -heartSize * 0.5, heartSize * 0.5);
      path.cubicTo(-heartSize * 0.5, heartSize * 0.8, -heartSize * 0.2,
          heartSize, 0, heartSize * 1.2);
      path.cubicTo(heartSize * 0.2, heartSize, heartSize * 0.5, heartSize * 0.8,
          heartSize * 0.5, heartSize * 0.5);
      path.cubicTo(heartSize, heartSize * 0.1, heartSize * 0.5,
          -heartSize * 0.1, 0, heartSize * 0.3);
      path.close();

      canvas.drawPath(path, glowPaint);
    }

    canvas.restore();
  }
}