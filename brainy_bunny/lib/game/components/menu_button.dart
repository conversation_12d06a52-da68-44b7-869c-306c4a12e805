// lib/game/components/menu_button.dart
import 'dart:math';
import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flame/events.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';

class MenuButton extends SpriteComponent with TapCallbacks, HasGameReference {
  final String imageAsset;
  final VoidCallback onTap;
  late Sprite lockIcon;
  bool _isLocked;

  MenuButton(this.imageAsset, this.onTap, {bool isLocked = false}) : _isLocked = isLocked;

  // FIXED: Make isLocked a proper getter/setter to force re-render
  bool get isLocked => _isLocked;
  set isLocked(bool value) {
    if (_isLocked != value) {
      _isLocked = value;
      // Force a re-render by marking the component as dirty
      if (isMounted) {
        removeFromParent();
        parent?.add(this);
      }
    }
  }

  @override
  Future<void> onLoad() async {
    sprite = await Sprite.load(imageAsset);
    lockIcon = await Sprite.load(AppConstants.LOCK_ICON_PATH);
    anchor = Anchor.center;

    // Add a gentle hover animation
    add(
      RotateEffect.by(
        0.1,
        EffectController(duration: 2, reverseDuration: 2, infinite: true),
      ),
    );

    if (kDebugMode) {
      print('🔘 MenuButton loaded: $imageAsset, isLocked: $_isLocked');
    }
  }

  @override
  void render(Canvas canvas) {
    // FIXED: Always call super.render first
    super.render(canvas);

    if (sprite == null) return;

    // FIXED: Create paint based on current lock state
    final paint = Paint();
    if (_isLocked) {
      paint.colorFilter = const ColorFilter.mode(Colors.grey, BlendMode.modulate);
    }

    // Render the main sprite
    sprite!.render(
      canvas,
      size: size,
      overridePaint: paint,
    );

    // FIXED: Only draw lock icon if actually locked
    if (_isLocked && size.x > 0) {
      final lockSize = size.x * 0.2;
      try {
        lockIcon.render(
          canvas,
          size: Vector2.all(lockSize),
          position: Vector2(size.x - lockSize, 0),
        );
      } catch (e) {
        if (kDebugMode) {
          print('❌ Error rendering lock icon: $e');
        }
      }
    }
  }

  @override
  void onTapDown(TapDownEvent event) {
    // Shrink button when pressed
    add(ScaleEffect.by(
      Vector2.all(0.9),
      EffectController(duration: 0.1, reverseDuration: 0.1),
    ));
  }

  @override
  void onTapUp(TapUpEvent event) {
    onTap();
  }

  @override
  void onTapCancel(TapCancelEvent event) {
    // Return to normal size when tap is canceled
    add(ScaleEffect.by(
      Vector2.all(1.1),
      EffectController(duration: 0.1),
    ));
  }

  /// BEAUTIFUL: Unlock with amazing animation sequence
  void unlockWithAnimation() {
    if (!_isLocked) return; // Already unlocked

    if (kDebugMode) {
      print('🎉 Starting beautiful unlock animation for: $imageAsset');
    }

    _isLocked = false;

    // Sequence of amazing unlock effects
    add(SequenceEffect([
      // 1. Flash effect (brightness increase)
      ColorEffect(
        Colors.white,
        EffectController(duration: 0.2),
        opacityFrom: 0,
        opacityTo: 0.8,
      ),

      // 2. Scale bounce and rotation effects
      SequenceEffect([
        ScaleEffect.by(
          Vector2.all(1.4),
          EffectController(duration: 0.5, curve: Curves.elasticOut),
        ),

        // Rotation wiggle
        RotateEffect.by(
          0.2,
          EffectController(duration: 0.1),
        ),
        RotateEffect.by(
          -0.4,
          EffectController(duration: 0.2),
        ),
        RotateEffect.by(
          0.2,
          EffectController(duration: 0.1),
        ),
      ]),

      // 3. Golden glow effect to show it's now unlocked
      ColorEffect(
        Colors.amber,
        EffectController(duration: 0.5),
        opacityFrom: 0,
        opacityTo: 0.4,
      ),

      // 4. Final scale back to normal with bounce
      ScaleEffect.to(
        Vector2.all(1.0),
        EffectController(duration: 0.3, curve: Curves.bounceOut),
      ),
    ]));

    // Add sparkle effect around the button
    _addSparkleEffect();

    // Add a gentle pulsing glow that fades away
    _addGlowEffect();
  }

  /// SIMPLE: Simple unlock without animations (for compatibility)
  void unlock() {
    if (kDebugMode) {
      print('🔓 Simple unlocking button: $imageAsset (was locked: $_isLocked)');
    }

    _isLocked = false;

    // Simple visual effect when unlocking
    add(
      ColorEffect(
        Colors.white,
        EffectController(duration: 0.3),
        opacityFrom: 0,
        opacityTo: 1,
      ),
    );
  }

  /// ADDED: Sparkle effect around the button
  void _addSparkleEffect() {
    try {
      // Create multiple small sparkle effects around the button
      for (int i = 0; i < 12; i++) {
        final angle = (i * 30.0) * (pi / 180.0); // Convert to radians
        final radius = size.x * 0.7;
        final sparklePosition = Vector2(
          radius * cos(angle),
          radius * sin(angle),
        );

        final sparkle = _SparkleComponent(
          position: sparklePosition,
          delay: i * 0.08, // Stagger the sparkles more
        );

        add(sparkle);

        // Sparkles will remove themselves automatically after animation
      }
    } catch (e) {
      // Sparkles are decorative, so if they fail, just continue
      if (kDebugMode) {
        print('Error adding sparkle effect: $e');
      }
    }
  }

  /// ADDED: Glow effect that pulses and fades
  void _addGlowEffect() {
    try {
      final glowComponent = _GlowComponent();
      add(glowComponent);
    } catch (e) {
      if (kDebugMode) {
        print('Error adding glow effect: $e');
      }
    }
  }
}

/// ADDED: Enhanced sparkle component for unlock animation
class _SparkleComponent extends PositionComponent {
  final double delay;
  bool _started = false;
  double _opacity = 0.0;
  double _animationTime = 0.0;
  static const double _totalDuration = 1.5; // Total animation duration in seconds
  late final Color _sparkleColor;

  _SparkleComponent({
    required Vector2 position,
    this.delay = 0.0,
  }) : super(position: position, size: Vector2.all(20)) {
    // Random sparkle colors - gold, yellow, white
    final colors = [Colors.yellow, Colors.amber, Colors.orange, Colors.white];
    _sparkleColor = colors[Random().nextInt(colors.length)];
  }

  @override
  Future<void> onLoad() async {
    // Start sparkle animation after delay
    Future.delayed(Duration(milliseconds: (delay * 1000).round()), () {
      if (isMounted && !_started) {
        _started = true;
        scale = Vector2.all(0.1);
      }
    });
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (_started) {
      _animationTime += dt;

      // Calculate opacity and scale based on animation time
      if (_animationTime < 0.3) {
        // Fast fade in phase
        final progress = _animationTime / 0.3;
        _opacity = progress;
        scale = Vector2.all(0.1 + (0.9 * progress));
      } else if (_animationTime < 0.8) {
        // Hold phase with slight twinkle
        _opacity = 0.8 + (0.2 * sin(_animationTime * 10));
        scale = Vector2.all(1.0 + (0.1 * sin(_animationTime * 8)));
      } else if (_animationTime < _totalDuration) {
        // Fade out phase
        final progress = (_animationTime - 0.8) / 0.7;
        _opacity = (1.0 - progress).clamp(0.0, 1.0);
        scale = Vector2.all((1.0 - progress * 0.5).clamp(0.1, 1.0));
      } else {
        // Animation complete - remove component
        if (isMounted) {
          removeFromParent();
        }
      }
    }
  }

  @override
  void render(Canvas canvas) {
    if (!_started || _opacity <= 0) return;

    // Draw a beautiful 6-pointed star sparkle
    final paint = Paint()
      ..color = _sparkleColor.withValues(alpha: _opacity)
      ..style = PaintingStyle.fill;

    final center = size / 2;
    final radius = size.x / 2;

    // Draw a 6-pointed star
    final path = Path();

    // Outer points
    for (int i = 0; i < 6; i++) {
      final angle = (i * 60.0) * (pi / 180.0);
      final x = center.x + radius * cos(angle);
      final y = center.y + radius * sin(angle);

      if (i == 0) {
        path.moveTo(x, y);
      } else {
        // Add curve to make it more star-like
        final prevAngle = ((i - 1) * 60.0) * (pi / 180.0);
        final prevX = center.x + radius * cos(prevAngle);
        final prevY = center.y + radius * sin(prevAngle);

        // Inner point
        final innerAngle = ((i - 0.5) * 60.0) * (pi / 180.0);
        final innerX = center.x + radius * 0.4 * cos(innerAngle);
        final innerY = center.y + radius * 0.4 * sin(innerAngle);

        path.lineTo(innerX, innerY);
        path.lineTo(x, y);
      }
    }

    // Close to first inner point
    final firstInnerAngle = ((-0.5) * 60.0) * (pi / 180.0);
    final firstInnerX = center.x + radius * 0.4 * cos(firstInnerAngle);
    final firstInnerY = center.y + radius * 0.4 * sin(firstInnerAngle);
    path.lineTo(firstInnerX, firstInnerY);
    path.close();

    canvas.drawPath(path, paint);

    // Add a bright glow effect
    final glowPaint = Paint()
      ..color = Colors.white.withValues(alpha: _opacity * 0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.0
      ..maskFilter = const MaskFilter.blur(BlurStyle.outer, 4.0);

    canvas.drawPath(path, glowPaint);
  }
}

/// ADDED: Glow component that creates a pulsing effect
class _GlowComponent extends PositionComponent {
  double _animationTime = 0.0;
  static const double _totalDuration = 2.0;

  _GlowComponent() : super(size: Vector2.all(0));

  @override
  void update(double dt) {
    super.update(dt);
    _animationTime += dt;

    if (_animationTime >= _totalDuration) {
      if (isMounted) {
        removeFromParent();
      }
    }
  }

  @override
  void render(Canvas canvas) {
    if (parent == null) return;

    final progress = (_animationTime / _totalDuration).clamp(0.0, 1.0);
    final fadeOut = 1.0 - progress;

    if (fadeOut <= 0) return;

    // Create pulsing glow around the button
    final parentSize = (parent as PositionComponent).size;
    final center = parentSize / 2;
    final glowRadius = parentSize.x * 0.6 * (1.0 + 0.3 * sin(_animationTime * 4));

    final glowPaint = Paint()
      ..color = Colors.amber.withValues(alpha: fadeOut * 0.3)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0
      ..maskFilter = MaskFilter.blur(BlurStyle.outer, 8.0 * fadeOut);

    canvas.drawCircle(
      Offset(center.x, center.y),
      glowRadius,
      glowPaint,
    );

    // Inner bright glow
    final innerGlowPaint = Paint()
      ..color = Colors.yellow.withValues(alpha: fadeOut * 0.2)
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.0
      ..maskFilter = MaskFilter.blur(BlurStyle.outer, 4.0 * fadeOut);

    canvas.drawCircle(
      Offset(center.x, center.y),
      glowRadius * 0.8,
      innerGlowPaint,
    );
  }
}