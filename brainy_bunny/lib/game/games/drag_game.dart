import 'dart:math';

import 'package:flame/components.dart';
import 'package:flame/effects.dart';
import 'package:flame/events.dart';
import 'package:flame/game.dart';
import 'package:flame/input.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/game/components/confetti_component.dart';
import 'package:brainy_bunny/game/components/drag_target_item.dart';
import 'package:brainy_bunny/game/components/draggable_item.dart';
import 'package:brainy_bunny/services/audio_service.dart';

class DragGame extends FlameGame with TapCallbacks {
  final String gameName;

  // Callback for returning to menu
  final VoidCallback? onReturnToMenu;

  int _matchedCount = 0;
  int _currentRound = 1;
  late int _pairsInCurrentRound;
  SpriteComponent? _background;
  ButtonComponent? _homeButton;
  bool _isGameComplete = false;

  // Flag to track if we've been properly mounted
  bool _isMounted = false;

  // Store the loaded background for later use
  Sprite? _backgroundSprite;

  // Store the home button sprite
  Sprite? _homeSprite;

  DragGame({
    required this.gameName,
    this.onReturnToMenu,
  });

  @override
  Future<void> onLoad() async {
    await super.onLoad();

    _matchedCount = 0;
    _currentRound = 1;
    _pairsInCurrentRound = AppConstants.ROUND_PAIRS[_currentRound - 1];
    _isGameComplete = false;
    _isMounted = false;

    if (kDebugMode) {
      print("Loading background for $gameName");
    }

    try {
      // Load the background and home button sprites
      _backgroundSprite = await loadSprite('$gameName/background.jpg');
      _homeSprite = await loadSprite(AppConstants.HOME_ICON_PATH);
    } catch (e) {
      if (kDebugMode) {
        print("Error loading sprites: $e");
      }
    }

    // UPDATED: Play random game music when game starts
    AudioService.instance.playGameMusic();

    // Need to wait for the game to be properly sized before continuing
    // This ensures the game layout is ready
    _isMounted = true;
  }

  @override
  void onGameResize(Vector2 gameSize) {
    super.onGameResize(gameSize);

    // Only proceed if we've been mounted and have a valid size
    if (_isMounted && gameSize.x > 0 && gameSize.y > 0) {
      // Add the background if it hasn't been added yet
      if (_background == null && _backgroundSprite != null) {
        _background = SpriteComponent(
          sprite: _backgroundSprite!,
          size: gameSize,
        );
        add(_background!);

        if (kDebugMode) {
          print("Background added with size: ${gameSize.x} x ${gameSize.y}");
        }

        // Add home button
        if (_homeButton == null && _homeSprite != null) {
          final buttonSize = Vector2(50, 50);
          _homeButton = ButtonComponent(
            button: SpriteComponent(
              sprite: _homeSprite!,
              size: buttonSize,
            ),
            position: Vector2(15, 15),
            onPressed: _returnToMenu,
            size: buttonSize,
          );
          add(_homeButton!);

          if (kDebugMode) {
            print("Home button added");
          }
        }

        // Now that we have a background, start loading the round
        // Use a microtask to ensure this happens after the current frame
        Future.microtask(() => _loadRound());
      }
    }
  }

  // Apply pop-in animation effect to component
  void _applyPopInEffect(PositionComponent component) {
    // Initial state: very small scale
    component.scale = Vector2.all(0.1);

    // Create sequence of effects for smooth appearance from center
    component.add(
      SequenceEffect([
        // Step 1: Quick increase to larger than needed size (120%)
        ScaleEffect.to(
          Vector2.all(1.2),
          EffectController(
            duration: 0.4,
            curve: Curves.easeOutBack, // Use curve with "spring" effect
          ),
        ),
        // Step 2: Smooth decrease to normal size
        ScaleEffect.to(
          Vector2.all(1.0),
          EffectController(
            duration: 0.2,
            curve: Curves.easeInOut,
          ),
        ),
      ]),
    );

    // Use opacity effect
    component.add(
      OpacityEffect.fadeIn(
        EffectController(
          duration: 0.3,
          curve: Curves.easeIn,
        ),
      ),
    );
  }

  // Apply pop-out animation effect to component
  void _applyPopOutEffect(
      PositionComponent component, VoidCallback? onComplete) {
    // Create sequence of effects for smooth disappearance from center
    component.add(
      SequenceEffect([
        // Step 1: Quick increase to 110%
        ScaleEffect.to(
          Vector2.all(1.1),
          EffectController(
            duration: 0.2,
            curve: Curves.easeOut,
          ),
        ),
        // Step 2: Quick decrease to very small size
        ScaleEffect.to(
          Vector2.all(0.1),
          EffectController(
            duration: 0.3,
            curve: Curves.easeIn,
          ),
        ),
      ])
        ..onComplete = onComplete,
    );

    // Use opacity effect
    component.add(
      OpacityEffect.fadeOut(
        EffectController(
          duration: 0.4,
          startDelay: 0.1,
          // Small delay so the fade starts after scaling begins
          curve: Curves.easeIn,
        ),
      ),
    );
  }

  void _returnToMenu() {
    if (!_isMounted || _isGameComplete) return;

    // Play button sound
    AudioService.instance.playSound('button_press.mp3');

    // Mark game as complete to prevent further updates
    _isGameComplete = true;

    if (kDebugMode) {
      print("Return to menu button pressed - stopping game music");
    }

    // CRITICAL: Stop game music immediately when manually returning to menu
    AudioService.instance.stopMusic();

    // Call the provided callback
    if (onReturnToMenu != null) {
      onReturnToMenu!();
    } else {
      // Fallback: just remove this game
      _cleanupAndRemove();
    }
  }

  Future<void> _loadRound() async {
    if (!_isMounted || _isGameComplete) {
      return; // Don't load if game is being removed or completed
    }

    if (kDebugMode) {
      print("Loading round $_currentRound");
    }

    _matchedCount = 0;
    _pairsInCurrentRound = AppConstants.ROUND_PAIRS[_currentRound - 1];

    // Clear all components except background and home button
    if (kDebugMode) {
      print("Clearing all components except background and home button");
    }

    final componentsToRemove = children
        .where(
            (component) => component != _background && component != _homeButton)
        .toList();
    for (final component in componentsToRemove) {
      remove(component);
    }

    // Load sprite assets
    if (kDebugMode) {
      print("Loading sprite assets");
    }

    final draggableSprites = await Future.wait(List.generate(
        5, (i) => loadSprite('$gameName/draggable_image_$i.png')));

    final dragTargetSprites = await Future.wait(List.generate(
        5, (i) => loadSprite('$gameName/drag_target_image_$i.png')));

    final acceptedTargetSprites = await Future.wait(List.generate(
        5, (i) => loadSprite('$gameName/drag_target_image_accepted_$i.png')));

    if (kDebugMode) {
      print("Sprite assets loaded");
    }

    // Guard against the game being removed while assets were loading
    if (!_isMounted || _isGameComplete) {
      return;
    }

    // Get the current game size - this should be valid now
    final currentSize = size;
    if (currentSize.x <= 0 || currentSize.y <= 0) {
      if (kDebugMode) {
        print("Invalid game size: $currentSize - deferring round loading");
      }

      // Try again in the next frame
      Future.microtask(() => _loadRound());
      return;
    }

    // Randomly select which pairs to show
    final random = Random();
    final indices = List<int>.generate(5, (i) => i)..shuffle(random);
    final selectedIndices = indices.take(_pairsInCurrentRound).toList();

    if (kDebugMode) {
      print("Selected indices for this round: $selectedIndices");
    }

    // Calculate component sizes based on current game size
    final maxWidth = currentSize.x / (_pairsInCurrentRound + 1);
    final maxHeight = currentSize.y / 3;
    final spriteSize = Vector2.all(maxWidth < maxHeight ? maxWidth : maxHeight);

    if (kDebugMode) {
      print("Calculated sprite size: $spriteSize");
    }

    // List for storing created components
    final List<PositionComponent> newComponents = [];

    // Add draggable items
    if (kDebugMode) {
      print("Adding draggable items");
    }

    for (int i = 0; i < selectedIndices.length; i++) {
      final index = selectedIndices[i];

      // Calculate the top-left position for the item
      final topLeftPosition = Vector2(
          currentSize.x / (_pairsInCurrentRound + 1) * (i + 1) -
              spriteSize.x / 2,
          currentSize.y / 10);

      // Calculate the center position for anchor.center
      final centerPosition = Vector2(
        topLeftPosition.x + spriteSize.x / 2,
        topLeftPosition.y + spriteSize.y / 2,
      );

      // Only add if the game is still active
      if (!_isMounted || _isGameComplete) return;

      final draggable = DraggableItem(
        index: index,
        sprite: draggableSprites[index],
        // IMPORTANT: Pass the center position as initialPosition since we'll use anchor.center
        initialPosition: centerPosition,
        size: spriteSize,
        gameRef: this,
      );

      // Set anchor to center
      draggable.anchor = Anchor.center;

      // Set position to the center position (this should match initialPosition)
      draggable.position = centerPosition;

      // Set initial opacity/scale for pop-in animation
      draggable.scale = Vector2.all(0.1);
      if (draggable.paint.color != Colors.transparent) {
        draggable.paint.color = draggable.paint.color.withValues(alpha: 0);
      }

      add(draggable);
      newComponents.add(draggable);

      if (kDebugMode) {
        print("Added draggable item $index at center position $centerPosition");
      }
    }

    // Add drag target items
    if (kDebugMode) {
      print("Adding drag target items");
    }

    selectedIndices.shuffle(random);
    for (int i = 0; i < selectedIndices.length; i++) {
      final index = selectedIndices[i];

      // Calculate the top-left position for the target
      final topLeftPosition = Vector2(
          currentSize.x / (_pairsInCurrentRound + 1) * (i + 1) -
              spriteSize.x / 2,
          currentSize.y / 2);

      // Calculate the center position for anchor.center
      final centerPosition = Vector2(
        topLeftPosition.x + spriteSize.x / 2,
        topLeftPosition.y + spriteSize.y / 2,
      );

      // Only add if the game is still active
      if (!_isMounted || _isGameComplete) return;

      final target = DragTargetItem(
        index: index,
        sprite: dragTargetSprites[index],
        acceptedSprite: acceptedTargetSprites[index],
        // Use the top-left position for targets since they don't need to return to original position
        position: topLeftPosition,
        size: spriteSize,
        gameRef: this,
      );

      // Set anchor to center
      target.anchor = Anchor.center;

      // Set position to the center position
      target.position = centerPosition;

      // Set initial opacity/scale for pop-in animation
      target.scale = Vector2.all(0.1);
      if (target.paint.color != Colors.transparent) {
        target.paint.color = target.paint.color.withValues(alpha: 0);
      }

      add(target);
      newComponents.add(target);

      if (kDebugMode) {
        print(
            "Added drag target item $index at center position $centerPosition");
      }
    }

    // Apply pop-in effect to all new components with a small delay between them
    for (var i = 0; i < newComponents.length; i++) {
      final component = newComponents[i];
      // Add a small delay between component appearances
      Future.delayed(Duration(milliseconds: 50 * i), () {
        if (_isMounted && !_isGameComplete) {
          _applyPopInEffect(component);
        }
      });
    }

    if (kDebugMode) {
      print("Round $_currentRound loaded with pop-in animations");
      _printComponentState();
    }
  }

  void onItemMatched() {
    if (!_isMounted || _isGameComplete) return;

    _matchedCount++;

    if (kDebugMode) {
      print("Item matched. Total matched: $_matchedCount");
    }

    if (_matchedCount >= _pairsInCurrentRound) {
      if (_currentRound < AppConstants.ROUND_PAIRS.length) {
        _currentRound++;

        if (kDebugMode) {
          print("Round complete. Moving to round $_currentRound");
        }

        _nextRound();
      } else {
        if (kDebugMode) {
          print("Game complete");
        }

        _gameComplete();
      }
    }
  }

  void _nextRound() {
    if (!_isMounted || _isGameComplete) return;

    if (kDebugMode) {
      print("Starting next round");
    }

    AudioService.instance.playSound('round_complete_sound.wav');

    // Delay before starting pop-out animation
    Future.delayed(const Duration(seconds: 3), () {
      if (!_isMounted || _isGameComplete) return;

      if (kDebugMode) {
        print("Starting pop-out animations for components");
      }

      // Collect all components except background and home button
      final componentsToAnimate = children
          .whereType<PositionComponent>()
          .where((component) =>
      component != _background && component != _homeButton)
          .toList();

      // Split into two groups for wave effect (top elements first, then bottom)
      final topComponents = componentsToAnimate
          .where((component) => component.position.y < size.y / 3)
          .toList();

      final bottomComponents = componentsToAnimate
          .where((component) => component.position.y >= size.y / 3)
          .toList();

      // Apply animation to top elements
      for (var i = 0; i < topComponents.length; i++) {
        final component = topComponents[i];

        Future.delayed(Duration(milliseconds: 50 * i), () {
          if (!_isMounted || _isGameComplete) return;

          _applyPopOutEffect(component, () {
            if (_isMounted &&
                !_isGameComplete &&
                children.contains(component)) {
              remove(component);
            }
          });
        });
      }

      // Apply animation to bottom elements with small additional delay
      final bottomDelay = 50 * topComponents.length;
      for (var i = 0; i < bottomComponents.length; i++) {
        final component = bottomComponents[i];

        Future.delayed(Duration(milliseconds: bottomDelay + 50 * i), () {
          if (!_isMounted || _isGameComplete) return;

          _applyPopOutEffect(component, () {
            if (_isMounted &&
                !_isGameComplete &&
                children.contains(component)) {
              remove(component);
            }
          });
        });
      }

      // Total animation time + small buffer
      final totalAnimationTime =
          bottomDelay + bottomComponents.length * 50 + 500;

      // Load next round after all animations complete
      Future.delayed(Duration(milliseconds: totalAnimationTime), () {
        if (kDebugMode) {
          print("Loading next round after pop-out animations");
        }
        if (!_isMounted || _isGameComplete) return;
        _loadRound();
      });
    });
  }

  void _gameComplete() {
    if (kDebugMode) {
      print("Game complete method called");
    }

    _isGameComplete = true;
    AudioService.instance.playSound('game_complete_sound.wav');

    // Ensure we wait a short moment before starting confetti
    Future.delayed(const Duration(milliseconds: 300), () {
      if (kDebugMode) {
        print("Creating confetti component");
      }

      // Add the enhanced confetti component with proper parameters
      final confetti = FlameConfetti();

      // Make sure component is properly added to the game
      add(confetti);

      if (kDebugMode) {
        print("Confetti component added");
      }

      // Create bursts at the center
      Future.delayed(const Duration(milliseconds: 100), () {
        if (kDebugMode) {
          print("Creating center confetti burst");
        }

        // Center burst with default enhanced parameters
        confetti.burst(Vector2(size.x / 2, size.y / 2));
      });
    });

    // Return to menu after celebration - adjusted delay
    Future.delayed(const Duration(seconds: 8), () {
      if (kDebugMode) {
        print("Game completed - stopping game music and returning to menu");
      }

      // CRITICAL: Stop game music when auto-returning to menu after completion
      AudioService.instance.stopMusic();

      // Call the return to menu callback instead of just cleanup
      if (onReturnToMenu != null) {
        onReturnToMenu!();
      } else {
        _cleanupAndRemove();
      }
    });
  }

  void _cleanupAndRemove() {
    _isMounted = false;
    _isGameComplete = true;

    // Safely clean up resources
    try {
      removeFromParent();
    } catch (e) {
      if (kDebugMode) {
        print('Error removing game from parent: $e');
      }
    }
  }

  @override
  void onRemove() {
    _isMounted = false;
    _isGameComplete = true;
    super.onRemove();
  }

  @override
  void removeFromParent() {
    _isMounted = false;

    // Make sure we're safely removed
    try {
      super.removeFromParent();
    } catch (e) {
      if (kDebugMode) {
        print('Error removing game from parent: $e');
      }
    }
  }

  void _printComponentState() {
    if (kDebugMode) {
      print("Current component state:");

      for (var component in children) {
        if (component is PositionComponent) {
          print(
              "${component.runtimeType}: position=${component.position}, size=${component.size}");
        } else {
          print("${component.runtimeType}: Not a PositionComponent");
        }
      }
    }
  }
}