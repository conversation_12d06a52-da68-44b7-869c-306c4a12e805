import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

/// Service to handle authentication-related operations
class AuthService {
  // Singleton pattern
  static final AuthService _instance = AuthService._internal();

  static AuthService get instance => _instance;

  late FirebaseAuth _auth;

  // Private constructor for singleton
  AuthService._internal();

  /// Initialize the auth service
  Future<void> initialize() async {
    final app = Firebase.app('brainy_bunny');
    _auth = FirebaseAuth.instanceFor(app: app);

    // Sign in anonymously if not already signed in
    await _ensureSignedIn();
  }

  /// Get the current authenticated user
  User? get currentUser => _auth.currentUser;

  /// Get the current user ID, or empty string if not authenticated
  String get userId => _auth.currentUser?.uid ?? '';

  /// Ensure the user is signed in anonymously
  Future<void> _ensureSignedIn() async {
    try {
      if (_auth.currentUser == null) {
        await _auth.signInAnonymously();
        if (kDebugMode) {
          print('Anonymous sign-in successful');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error during anonymous sign-in: $e');
      }
    }
  }

  /// Sign out the current user
  Future<void> signOut() async {
    try {
      await _auth.signOut();
    } catch (e) {
      if (kDebugMode) {
        print('Error during sign-out: $e');
      }
    }
  }
}
