// lib/ui/screens/home_screen.dart
import 'package:flame/game.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/game/core/toddler_game_menu.dart';
import 'package:brainy_bunny/services/audio_service.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';
import 'package:brainy_bunny/ui/overlays/purchase_flow_overlays.dart';
import 'package:brainy_bunny/ui/overlays/purchase_overlays.dart';

class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> with WidgetsBindingObserver {
  ToddlerGameMenu? _gameMenu;
  late Future<void> _initializationFuture;
  bool _isInBackground = false;

  // Purchase manager instance
  late PurchaseManager _purchaseManager;
  late GameWidget<ToddlerGameMenu> _gameWidget;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);

    // CRITICAL: Force landscape orientation immediately and permanently
    _setLandscapeOrientation();

    // Initialize purchase manager
    _purchaseManager = PurchaseManager();

    _initializationFuture = _initializeGame();
  }

  /// ENHANCED: Force landscape orientation permanently
  void _setLandscapeOrientation() {
    SystemChrome.setPreferredOrientations([
      DeviceOrientation.landscapeLeft,
      DeviceOrientation.landscapeRight,
    ]);

    // Also set landscape system UI mode
    SystemChrome.setSystemUIOverlayStyle(const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      systemNavigationBarColor: Colors.transparent,
    ));

    if (kDebugMode) {
      print('🔄 Forced landscape orientation');
    }
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
        _isInBackground = true;
        _pauseGame();
        break;
      case AppLifecycleState.resumed:
        if (_isInBackground) {
          _isInBackground = false;
          // CRITICAL: Re-enforce landscape when returning
          _setLandscapeOrientation();
          _resumeGame();
          _refreshGameLayoutAfterDelay();
        }
        break;
      case AppLifecycleState.inactive:
        _pauseGame();
        break;
    }
  }

  /// ADDED: Refresh game layout after orientation change
  void _refreshGameLayoutAfterDelay() {
    Future.delayed(const Duration(milliseconds: 500), () {
      if (mounted && _gameMenu != null) {
        try {
          // Force the game to recalculate its size
          final currentSize = _gameMenu!.size;
          _gameMenu!.onGameResize(Vector2(currentSize.x + 1, currentSize.y + 1));

          // Then restore correct size and trigger layout refresh
          Future.delayed(const Duration(milliseconds: 100), () {
            if (mounted && _gameMenu != null) {
              _gameMenu!.onGameResize(currentSize);

              // Force menu grid relayout
              _gameMenu!.menuGridComponent.forceRelayout();
            }
          });
        } catch (e) {
          if (kDebugMode) {
            print('❌ Error refreshing game layout: $e');
          }
        }
      }
    });
  }

  void _pauseGame() {
    // FIXED: Only pause audio, not the entire game engine to prevent Android freezes
    AudioService.instance.pauseMusic();
    // Removed: _gameMenu?.pauseEngine(); - this was causing Android freezes after screenshots
  }

  void _resumeGame() {
    // ENHANCED: Resume menu music specifically since we're in HomeScreen
    AudioService.instance.resumeMusicOfType('menu');
    // Removed: _gameMenu?.resumeEngine(); - not needed since we don't pause the engine anymore
  }

  Future<void> _initializeGame() async {
    // Services should already be initialized by splash screen
    // Just ensure purchase manager is ready
    if (!_purchaseManager.isInitialized) {
      await _purchaseManager.initialize();

      if (kDebugMode) {
        print('🔄 Purchase manager status: ${_purchaseManager.isPurchased}');
        print('🔄 Price: ${_purchaseManager.displayPrice}');
        print('🔄 Using fallback: ${_purchaseManager.isUsingFallbackPrice}');
      }
    }

    // Initialize audio service
    await AudioService.instance.initialize();

    if (kDebugMode) {
      print('🎮 HomeScreen - Game initialization started');
      print('   Purchase manager initialized: ${_purchaseManager.isInitialized}');
      print('   Purchase status: ${_purchaseManager.isPurchased}');
    }

    final gameMenu = ToddlerGameMenu();

    // Setup callback functions with proper overlay management
    _purchaseManager.onPurchaseSuccess = () => _onPurchaseSuccess(gameMenu);
    _purchaseManager.onPurchaseError = () => _onPurchaseError(gameMenu);
    _purchaseManager.onPurchasePending = () => _onPurchasePending(gameMenu);
    _purchaseManager.onPurchasePendingRemove = () => _onPurchasePendingRemove(gameMenu);
    _purchaseManager.onPurchaseInvalid = () => _onPurchaseInvalid(gameMenu);

    _gameMenu = gameMenu;

    // MOVED: Start menu music AFTER game menu is created and ready
    if (kDebugMode) {
      print('🎵 Starting menu music after game menu initialization');
    }

    // Add delay to ensure game is fully loaded before starting music
    Future.delayed(const Duration(milliseconds: 100), () {
      AudioService.instance.forcePlayMenuMusic();

      if (kDebugMode) {
        print('✅ Menu music force started after game initialization');
      }
    });

    if (kDebugMode) {
      print('✅ HomeScreen - Game initialization completed');
    }
  }

  // ENHANCED: Purchase success with beautiful animations
  void _onPurchaseSuccess(ToddlerGameMenu gameMenu) {
    if (mounted) {
      if (kDebugMode) {
        print('🎉 Purchase success callback triggered - unlock animation will be handled by gameMenu');
      }

      // CRITICAL: Force landscape orientation immediately
      _setLandscapeOrientation();

      // CRITICAL: Force resume the game in case it's paused
      _resumeGame();

      // Remove all purchase overlays first
      _clearAllPurchaseOverlays(gameMenu);

      // The game menu will handle the unlock animation itself
      gameMenu.onPurchaseSuccessWithAnimation();

      // NO setState here - the game menu handles its own state

      // ADDED: Schedule layout refresh after success
      _scheduleLayoutRefreshAfterPurchase();

      if (kDebugMode) {
        print('✅ Purchase success - animation delegated to game menu');
      }
    }
  }

  /// ADDED: Schedule layout refresh after purchase success
  void _scheduleLayoutRefreshAfterPurchase() {
    // Refresh layout multiple times to ensure it's correct
    final refreshTimes = [500, 1000, 2000]; // milliseconds

    for (final delay in refreshTimes) {
      Future.delayed(Duration(milliseconds: delay), () {
        if (mounted) {
          _setLandscapeOrientation();
          _refreshGameLayoutAfterDelay();
        }
      });
    }
  }

  void _onPurchaseError(ToddlerGameMenu gameMenu) {
    // CRITICAL: Re-enforce landscape after error
    _setLandscapeOrientation();

    _clearAllPurchaseOverlays(gameMenu);
    gameMenu.overlays.add(AppConstants.PURCHASE_ERROR_OVERLAY);

    // Schedule layout refresh
    _refreshGameLayoutAfterDelay();

    if (kDebugMode) {
      print('❌ Purchase error - showing error overlay');
    }
  }

  void _onPurchasePending(ToddlerGameMenu gameMenu) {
    // Remove other overlays before showing pending
    _clearAllPurchaseOverlays(gameMenu);
    gameMenu.overlays.add(AppConstants.PENDING_PURCHASE_OVERLAY);

    if (kDebugMode) {
      print('⏳ Purchase pending - showing pending overlay');
    }
  }

  void _onPurchasePendingRemove(ToddlerGameMenu gameMenu) {
    // CRITICAL: Re-enforce landscape when purchase dialog closes
    _setLandscapeOrientation();

    // Remove only pending overlay
    if (gameMenu.overlays.isActive(AppConstants.PENDING_PURCHASE_OVERLAY)) {
      gameMenu.overlays.remove(AppConstants.PENDING_PURCHASE_OVERLAY);
      if (kDebugMode) {
        print('🧹 Removed pending overlay');
      }
    }

    // ADDED: Refresh layout after pending overlay removal
    _refreshGameLayoutAfterDelay();
  }

  void _onPurchaseInvalid(ToddlerGameMenu gameMenu) {
    // CRITICAL: Re-enforce landscape after invalid purchase
    _setLandscapeOrientation();

    _clearAllPurchaseOverlays(gameMenu);
    gameMenu.overlays.add(AppConstants.INVALID_PURCHASE_OVERLAY);

    // Schedule layout refresh
    _refreshGameLayoutAfterDelay();

    if (kDebugMode) {
      print('⚠️ Invalid purchase - showing invalid overlay');
    }
  }

  void _clearAllPurchaseOverlays(ToddlerGameMenu gameMenu) {
    final overlaysToRemove = [
      AppConstants.PENDING_PURCHASE_OVERLAY,
      AppConstants.PURCHASE_ERROR_OVERLAY,
      AppConstants.INVALID_PURCHASE_OVERLAY,
    ];

    for (final overlayId in overlaysToRemove) {
      if (gameMenu.overlays.isActive(overlayId)) {
        gameMenu.overlays.remove(overlayId);
        if (kDebugMode) {
          print('🧹 Removed overlay: $overlayId');
        }
      }
    }
  }

  // REMOVED: _retryPurchase method - retry functionality removed as requested

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: FutureBuilder(
        future: _initializationFuture,
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.done) {
            final gameMenu = _gameMenu;
            if (gameMenu == null) {
              return const Center(
                child: Text('Game initialization error'),
              );
            }

            // Create the game widget
            _gameWidget = GameWidget<ToddlerGameMenu>(
              game: gameMenu,
              overlayBuilderMap: {
                // REMOVED: Loading overlay completely as requested
                // AppConstants.LOADING_OVERLAY: (context, game) => const LoadingOverlay(),
                AppConstants.PENDING_PURCHASE_OVERLAY: (context, game) => const PurchasePendingOverlay(),

                // FIXED: Purchase error overlay without retry button
                AppConstants.PURCHASE_ERROR_OVERLAY: (context, game) => PurchaseErrorOverlay(
                  message: 'Purchase failed. If you already own this, try restarting the app.',
                  onClose: () {
                    _setLandscapeOrientation(); // Force landscape
                    _clearAllPurchaseOverlays(game);
                    _refreshGameLayoutAfterDelay(); // Refresh layout
                  },
                ),

                // ENHANCED: Invalid purchase overlay with layout refresh
                AppConstants.INVALID_PURCHASE_OVERLAY: (context, game) => PurchaseErrorOverlay(
                  message: 'Purchase verification failed. Please restart the app.',
                  onClose: () {
                    _setLandscapeOrientation(); // Force landscape
                    _clearAllPurchaseOverlays(game);
                    _refreshGameLayoutAfterDelay(); // Refresh layout
                  },
                ),

                // New purchase flow overlays
                AppConstants.PURCHASE_OFFER_OVERLAY: (context, game) => PurchaseOfferOverlay(
                  gameWidget: _gameWidget,
                ),
                AppConstants.AGE_VERIFICATION_OVERLAY: (context, game) => AgeVerificationOverlay(
                  gameWidget: _gameWidget,
                ),
              },
            );

            // ADDED: Ensure menu music starts when widget is fully built
            WidgetsBinding.instance.addPostFrameCallback((_) {
              if (kDebugMode) {
                print('🎵 Widget fully built - ensuring menu music is playing');
              }
              AudioService.instance.forcePlayMenuMusic();
            });

            // CLEAN: Just return the game widget without debug elements
            return _gameWidget;

          } else if (snapshot.hasError) {
            return Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error: ${snapshot.error}'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () {
                      setState(() {
                        _initializationFuture = _initializeGame();
                      });
                    },
                    child: const Text('Try Again'),
                  ),
                ],
              ),
            );
          } else {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CircularProgressIndicator(),
                  SizedBox(height: 16),
                  Text('Loading game...'),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    AudioService.instance.stopMusic();

    // Dispose purchase manager resources
    _purchaseManager.dispose();

    super.dispose();
  }
}