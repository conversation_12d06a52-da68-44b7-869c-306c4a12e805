import 'package:flame/game.dart';
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/game/games/drag_game.dart';
import 'package:brainy_bunny/ui/overlays/loading_overlay.dart';

class GameScreen extends StatefulWidget {
  final String gameName;

  const GameScreen({
    super.key,
    required this.gameName,
  });

  @override
  State<GameScreen> createState() => _GameScreenState();
}

class _GameScreenState extends State<GameScreen> with WidgetsBindingObserver {
  late DragGame _game;

  @override
  void initState() {
    super.initState();
    WidgetsBinding.instance.addObserver(this);
    _game = DragGame(gameName: widget.gameName);
  }

  @override
  void didChangeAppLifecycleState(AppLifecycleState state) {
    super.didChangeAppLifecycleState(state);

    switch (state) {
      case AppLifecycleState.paused:
      case AppLifecycleState.detached:
      case AppLifecycleState.hidden:
      case AppLifecycleState.inactive:
        // FIXED: Only pause audio, not the entire game engine to prevent Android freezes
        // Removed: _game.pauseEngine(); - this was causing Android freezes after screenshots
        break;
      case AppLifecycleState.resumed:
        // FIXED: No need to resume engine since we don't pause it anymore
        // Removed: _game.resumeEngine(); - not needed since we don't pause the engine
        break;
    }
  }

  @override
  Widget build(BuildContext context) {
    return WillPopScope(
      onWillPop: () async {
        Navigator.of(context).pop();
        return false;
      },
      child: Scaffold(
        body: GameWidget<DragGame>(
          game: _game,
          // REMOVED: loadingBuilder - loading overlay removed completely as requested
          overlayBuilderMap: {
            // REMOVED: Loading overlay completely as requested
            // AppConstants.LOADING_OVERLAY: (context, game) => const LoadingOverlay(),
          },
        ),
      ),
    );
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    super.dispose();
  }
}