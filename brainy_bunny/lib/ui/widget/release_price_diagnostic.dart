// lib/ui/widgets/release_price_diagnostic.dart
import 'package:flutter/material.dart';
import 'package:brainy_bunny/constants/app_constants.dart';
import 'package:brainy_bunny/services/purchase_manager.dart';

/// Release-mode diagnostic widget for price issues
/// Works in both debug and release builds
class ReleasePriceDiagnostic extends StatefulWidget {
  final PurchaseManager purchaseManager;

  const ReleasePriceDiagnostic({
    super.key,
    required this.purchaseManager,
  });

  @override
  State<ReleasePriceDiagnostic> createState() => _ReleasePriceDiagnosticState();
}

class _ReleasePriceDiagnosticState extends State<ReleasePriceDiagnostic> {
  bool _showDiagnostic = false;
  String _lastRefreshResult = '';
  bool _isRefreshing = false;

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Diagnostic toggle button (always visible for testing)
        Container(
          width: double.infinity,
          padding: const EdgeInsets.all(8),
          child: ElevatedButton(
            onPressed: () {
              setState(() {
                _showDiagnostic = !_showDiagnostic;
              });
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: _showDiagnostic ? Colors.green : Colors.blue,
              foregroundColor: Colors.white,
            ),
            child: Text(_showDiagnostic ? 'Hide Price Info' : 'Show Price Info'),
          ),
        ),

        // Diagnostic panel
        if (_showDiagnostic) ...[
          const SizedBox(height: 8),
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Colors.grey.shade100,
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: Colors.grey.shade300),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '💰 Price Diagnostic',
                  style: TextStyle(
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                    color: Colors.blue.shade700,
                  ),
                ),
                const SizedBox(height: 16),

                // Current price info
                _buildInfoCard(
                  'Current Price',
                  widget.purchaseManager.displayPrice,
                  widget.purchaseManager.isUsingFallbackPrice
                      ? Colors.orange.shade700
                      : Colors.green.shade700,
                  widget.purchaseManager.isUsingFallbackPrice
                      ? Icons.warning
                      : Icons.check_circle,
                ),

                const SizedBox(height: 12),

                // Status info
                _buildInfoCard(
                  'Price Status',
                  widget.purchaseManager.isUsingFallbackPrice
                      ? 'Using Fallback (\${AppConstants.FALLBACK_PRICE})'
                      : 'Real Store Price Loaded',
                  widget.purchaseManager.isUsingFallbackPrice
                      ? Colors.orange.shade700
                      : Colors.green.shade700,
                  widget.purchaseManager.isUsingFallbackPrice
                      ? Icons.error_outline
                      : Icons.storefront,
                ),

                const SizedBox(height: 12),

                // Product details if available
                if (widget.purchaseManager.productDetails != null) ...[
                  _buildInfoCard(
                    'Product Details',
                    '${widget.purchaseManager.productDetails!.title}\n'
                        'Price: ${widget.purchaseManager.productDetails!.price}\n'
                        'Currency: ${widget.purchaseManager.productDetails!.currencyCode}',
                    Colors.blue.shade700,
                    Icons.info,
                  ),
                  const SizedBox(height: 12),
                ],

                // Error info if available
                if (widget.purchaseManager.priceLoadError != null) ...[
                  _buildInfoCard(
                    'Error',
                    widget.purchaseManager.priceLoadError!,
                    Colors.red.shade700,
                    Icons.error,
                  ),
                  const SizedBox(height: 12),
                ],

                // Expected vs actual
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: widget.purchaseManager.displayPrice.contains('0.06')
                        ? Colors.green.shade50
                        : Colors.orange.shade50,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: widget.purchaseManager.displayPrice.contains('0.06')
                          ? Colors.green.shade300
                          : Colors.orange.shade300,
                    ),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Icon(
                            widget.purchaseManager.displayPrice.contains('0.06')
                                ? Icons.check_circle
                                : Icons.help_outline,
                            color: widget.purchaseManager.displayPrice.contains('0.06')
                                ? Colors.green.shade700
                                : Colors.orange.shade700,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            'Expected: €0.06',
                            style: TextStyle(
                              fontWeight: FontWeight.bold,
                              color: Colors.grey.shade700,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Actual: ${widget.purchaseManager.displayPrice}',
                        style: TextStyle(
                          fontWeight: FontWeight.bold,
                          color: widget.purchaseManager.displayPrice.contains('0.06')
                              ? Colors.green.shade700
                              : Colors.orange.shade700,
                        ),
                      ),
                      if (!widget.purchaseManager.displayPrice.contains('0.06')) ...[
                        const SizedBox(height: 8),
                        Text(
                          '⚠️ Real price not loading from Google Play Store',
                          style: TextStyle(
                            fontSize: 12,
                            color: Colors.orange.shade700,
                            fontStyle: FontStyle.italic,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),

                const SizedBox(height: 16),

                // Action buttons
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _isRefreshing ? null : _refreshPrice,
                        icon: _isRefreshing
                            ? const SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                            : const Icon(Icons.refresh),
                        label: Text(_isRefreshing ? 'Refreshing...' : 'Refresh Price'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.blue.shade600,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: _testPurchaseFlow,
                        icon: const Icon(Icons.shopping_cart),
                        label: const Text('Test Purchase'),
                        style: ElevatedButton.styleFrom(
                          backgroundColor: Colors.green.shade600,
                          foregroundColor: Colors.white,
                        ),
                      ),
                    ),
                  ],
                ),

                // Last refresh result
                if (_lastRefreshResult.isNotEmpty) ...[
                  const SizedBox(height: 12),
                  Container(
                    width: double.infinity,
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: Colors.blue.shade50,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(color: Colors.blue.shade200),
                    ),
                    child: Text(
                      _lastRefreshResult,
                      style: TextStyle(
                        fontSize: 12,
                        color: Colors.blue.shade700,
                      ),
                    ),
                  ),
                ],

                const SizedBox(height: 16),

                // Troubleshooting tips for release builds
                _buildTroubleshootingSection(),
              ],
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildInfoCard(String title, String content, Color color, IconData icon) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: color,
                    fontSize: 12,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  content,
                  style: TextStyle(
                    color: Colors.grey.shade700,
                    fontSize: 13,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTroubleshootingSection() {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.amber.shade50,
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.amber.shade300),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb_outline, color: Colors.amber.shade700, size: 20),
              const SizedBox(width: 8),
              Text(
                'Troubleshooting for Release Builds',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  color: Colors.amber.shade700,
                  fontSize: 14,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8          ),
          ...getTroubleshootingSteps().map((step) => Padding(
            padding: const EdgeInsets.only(bottom: 4),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '• ',
                  style: TextStyle(color: Colors.amber.shade700, fontWeight: FontWeight.bold),
                ),
                Expanded(
                  child: Text(
                    step,
                    style: TextStyle(
                      color: Colors.grey.shade700,
                      fontSize: 12,
                    ),
                  ),
                ),
              ],
            ),
          )),
        ],
      ),
    );
  }

  List<String> getTroubleshootingSteps() {
    final steps = <String>[];

    if (widget.purchaseManager.isUsingFallbackPrice) {
      steps.addAll([
        'Verify product "full_game_unlock" exists in Google Play Console',
        'Check product is ACTIVE (not Draft) in Play Console',
        'Ensure app is published to Internal Testing track',
        'Verify your Google account is added as Internal Tester',
        'Install app from Play Store testing link (not direct APK)',
        'Check if device region matches product availability',
        'Verify Google Play Services is installed and updated',
        'Try switching to different Google account for testing',
        'Check if pricing is correctly configured for your region',
      ]);
    } else {
      steps.addAll([
        'Real price loaded successfully!',
        'If purchase still shows wrong price, clear Google Play Store cache',
        'Restart device and try again',
      ]);
    }

    return steps;
  }

  Future<void> _refreshPrice() async {
    setState(() {
      _isRefreshing = true;
      _lastRefreshResult = '';
    });

    try {
      final oldPrice = widget.purchaseManager.displayPrice;

      await widget.purchaseManager.refreshPrice(forceRefresh: true);

      // Wait a moment for the price to update
      await Future.delayed(const Duration(milliseconds: 500));

      final newPrice = widget.purchaseManager.displayPrice;

      setState(() {
        _lastRefreshResult = 'Refresh completed!\n'
            'Old: $oldPrice\n'
            'New: $newPrice\n'
            'Changed: ${oldPrice != newPrice ? "Yes" : "No"}\n'
            'Time: ${DateTime.now().toString().substring(11, 19)}';
      });

    } catch (e) {
      setState(() {
        _lastRefreshResult = 'Refresh failed: $e';
      });
    } finally {
      setState(() {
        _isRefreshing = false;
      });
    }
  }

  Future<void> _testPurchaseFlow() async {
    try {
      // Show confirmation dialog
      final confirmed = await showDialog<bool>(
        context: context,
        builder: (context) => AlertDialog(
          title: const Text('Test Purchase Flow'),
          content: Text(
            'This will start the purchase process with current price: ${widget.purchaseManager.displayPrice}\n\n'
                'You can cancel at any step to avoid actual purchase.',
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context, false),
              child: const Text('Cancel'),
            ),
            ElevatedButton(
              onPressed: () => Navigator.pop(context, true),
              child: const Text('Start Test'),
            ),
          ],
        ),
      );

      if (confirmed == true) {
        await widget.purchaseManager.purchaseFullGame();

        // Show result
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Purchase flow started - check overlays'),
              backgroundColor: Colors.green,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Test failed: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }
}

/// Floating diagnostic button for release builds
class ReleaseFloatingDiagnostic extends StatelessWidget {
  final PurchaseManager purchaseManager;

  const ReleaseFloatingDiagnostic({
    super.key,
    required this.purchaseManager,
  });

  @override
  Widget build(BuildContext context) {
    return Positioned(
      top: 50,
      right: 16,
      child: FloatingActionButton(
        mini: true,
        backgroundColor: purchaseManager.isUsingFallbackPrice
            ? Colors.orange.shade600
            : Colors.green.shade600,
        foregroundColor: Colors.white,
        onPressed: () {
          showDialog(
            context: context,
            builder: (context) => Dialog(
              child: Container(
                constraints: BoxConstraints(
                  maxHeight: MediaQuery.of(context).size.height * 0.8,
                  maxWidth: MediaQuery.of(context).size.width * 0.9,
                ),
                child: Column(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Header
                    Container(
                      padding: const EdgeInsets.all(16),
                      decoration: BoxDecoration(
                        color: Colors.blue.shade600,
                        borderRadius: const BorderRadius.only(
                          topLeft: Radius.circular(8),
                          topRight: Radius.circular(8),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Icon(Icons.analytics, color: Colors.white),
                          const SizedBox(width: 8),
                          const Expanded(
                            child: Text(
                              'Price Diagnostic',
                              style: TextStyle(
                                color: Colors.white,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          IconButton(
                            onPressed: () => Navigator.pop(context),
                            icon: const Icon(Icons.close, color: Colors.white),
                          ),
                        ],
                      ),
                    ),

                    // Content
                    Flexible(
                      child: SingleChildScrollView(
                        child: Padding(
                          padding: const EdgeInsets.all(16),
                          child: ReleasePriceDiagnostic(purchaseManager: purchaseManager),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          );
        },
        child: purchaseManager.isUsingFallbackPrice
            ? const Icon(Icons.warning)
            : const Icon(Icons.check),
      ),
    );
  }
}