// File generated by FlutterFire CLI.
// ignore_for_file: type=lint
import 'package:firebase_core/firebase_core.dart' show FirebaseOptions;
import 'package:flutter/foundation.dart'
    show defaultTargetPlatform, kIsWeb, TargetPlatform;

/// Default [FirebaseOptions] for use with your Firebase apps.
///
/// Example:
/// ```dart
/// import 'firebase_options.dart';
/// // ...
/// await Firebase.initializeApp(
///   options: DefaultFirebaseOptions.currentPlatform,
/// );
/// ```
class DefaultFirebaseOptions {
  static FirebaseOptions get currentPlatform {
    if (kIsWeb) {
      return web;
    }
    switch (defaultTargetPlatform) {
      case TargetPlatform.android:
        return android;
      case TargetPlatform.iOS:
        return ios;
      case TargetPlatform.macOS:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for macos - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.windows:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for windows - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      case TargetPlatform.linux:
        throw UnsupportedError(
          'DefaultFirebaseOptions have not been configured for linux - '
          'you can reconfigure this by running the FlutterFire CLI again.',
        );
      default:
        throw UnsupportedError(
          'DefaultFirebaseOptions are not supported for this platform.',
        );
    }
  }

  static const FirebaseOptions web = FirebaseOptions(
    apiKey: 'AIzaSyCz88wNkmHNZafkDy6PnZtQgHlbstkpgvA',
    appId: '1:24755023396:web:3d186076a372e8179032b1',
    messagingSenderId: '24755023396',
    projectId: 'brainy-bunny',
    authDomain: 'brainy-bunny.firebaseapp.com',
    storageBucket: 'brainy-bunny.firebasestorage.app',
  );

  static const FirebaseOptions android = FirebaseOptions(
    apiKey: 'AIzaSyB5gYh6kA9yBDOwuHu4IAEQqCX3MB3uRcU',
    appId: '1:24755023396:android:24372c80047f16279032b1',
    messagingSenderId: '24755023396',
    projectId: 'brainy-bunny',
    storageBucket: 'brainy-bunny.firebasestorage.app',
  );

  static const FirebaseOptions ios = FirebaseOptions(
    apiKey: 'AIzaSyBYm4lKlP7HzczSYPLZpuPYZ2amE7s3EgM',
    appId: '1:24755023396:ios:fb33af5ee71110e99032b1',
    messagingSenderId: '24755023396',
    projectId: 'brainy-bunny',
    storageBucket: 'brainy-bunny.firebasestorage.app',
    iosBundleId: 'com.goodkarmalab.brainyBunny',
  );

}