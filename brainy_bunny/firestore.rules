// firestore.rules - Security rules for Firestore
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {

    // Rules for Brainy Bunny purchases by user UID
    match /brainy_bunny_purchases/{userId} {
      // Allow read if authenticated as this user
      allow read: if request.auth != null && request.auth.uid == userId;

      // Allow write if authenticated as this user
      allow write: if request.auth != null &&
                      request.auth.uid == userId &&
                      validatePurchaseData();
    }

    // Rules for purchases by device ID (for restoration)
    match /brainy_bunny_device_purchases/{deviceId} {
      // Any authenticated user can read device purchases
      // (required for purchase restoration)
      allow read: if request.auth != null;

      // Allow write only if device_id in data matches document ID
      allow write: if request.auth != null &&
                      request.resource.data.device_id == deviceId &&
                      validatePurchaseData();
    }

    // Purchase data validation function
    function validatePurchaseData() {
      let data = request.resource.data;

      return (
        // Check presence of all required fields
        data.keys().hasAll(['product_id', 'purchase_date', 'platform', 'device_id']) &&

        // Check product_id correctness
        data.product_id == 'full_game_unlock' &&

        // Check platform correctness
        data.platform in ['ios', 'android'] &&

        // Check that purchase_date is a timestamp
        data.purchase_date is timestamp &&

        // Check that device_id is a string
        data.device_id is string &&
        data.device_id.size() > 0 &&

        // Check app_version if present
        (!data.keys().hasAny(['app_version']) || data.app_version is string)
      );
    }

    // Deny all other operations
    match /{document=**} {
      allow read, write: if false;
    }
  }
}